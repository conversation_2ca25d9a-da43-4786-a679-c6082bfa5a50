# CreateOpportunity Function - Complete Flow Documentation

## Overview

The `createOpportunity` function is the main entry point for creating new opportunities in the system. It has been optimized to follow best practices with proper error handling, transaction management, and modular design.

## Function Signature

```typescript
async createOpportunity(
    memberId: string,
    companyId: string,
    opportunityDto: CreateWarrantyOpportunityDto,
    warrantyType: boolean,
): Promise<CreatedResponse>
```

## Step-by-Step Detailed Flow

### Phase 1: Input Validation & Setup

1. **Input Validation** (`validateCreateOpportunityInput`)

    - Validate `memberId` is a non-empty string
    - Validate `companyId` is a non-empty string
    - Validate `opportunityDto` exists and contains required fields:
        - `contactId` is required
        - `oppType` is required
        - `oppDate` is required
        - `newLeadDate` is required
    - Throw `BadRequestException` if any validation fails

2. **Database Session Initialization**

    - Start MongoDB session for transaction management
    - Begin transaction to ensure data consistency

3. **Data Preparation**
    - Clean input data using `cleanCreateData()` helper
    - Generate unique `opportunityId` using `randomUUID()`

### Phase 2: Data Validation & Preparation

4. **Contact Validation** (`validateAndFetchContact`)

    - Query database for contact with `contactId` and `companyId`
    - Fetch only required fields: `isBusiness`, `lastName`, `firstName`, `businessName`, `createdAt`, `type`
    - Throw `BadRequestException` if contact not found
    - Return typed `ContactForOpportunity` object

5. **PO & Number Generation** (`generatePOAndNumber`)
    - Extract contact display name using business logic:
        - If business: use `businessName`
        - If individual: use `lastName` or `firstName`
        - Fallback to "UNKN" if no name available
    - Generate PO using `createPO(name, street)` helper
    - Generate opportunity number using `getNum()` method
    - Return `{ PO, num }` object

### Phase 3: Business Logic Processing

6. **Contact Transfer Handling** (`handleContactTransfer`)

    - Check if `originalContact` differs from `contactId`
    - If transfer needed:
        - Find existing active leads for target contact
        - Transfer active lead from original to target contact
        - Mark existing lead as invalid if target already has active lead
        - Link original contact to target contact with "Other" relationship

7. **Lead Management** (`handleLeadManagement`)

    - Get default lead stage for company
    - Branch based on `createLead` flag:

    **If Creating New Lead:**

    - Mark existing lead as "lost" if `leadId` provided
    - Create new lead with "converted" status
    - Link lead to opportunity
    - Add contact activity log

    **If Converting Existing Lead:**

    - If `leadId` provided: convert specific lead
    - If no `leadId`: find most recent active lead
    - If no active lead exists: create new lead
    - Update lead status to "converted"
    - Add contact activity log if conversion successful

### Phase 4: Record Creation

8. **Opportunity Record Creation** (`createOpportunityRecord`)

    - Build comments array from DTO and notes
    - Get tax jurisdiction if only one exists for state
    - Build checkpoint activity object with timestamps
    - Clean DTO by removing unwanted fields
    - Set sale date for warranty opportunities
    - Create opportunity document with all data
    - Save to database within transaction

9. **Activity Record Creation** (`createOpportunityActivity`)
    - Create activity log for opportunity module
    - Add "created New Opportunity" activity entry
    - Save activity document within transaction

### Phase 5: Contact Updates & Finalization

10. **Contact Information Update** (`updateContactAfterOpportunityCreation`)

    -   Update contact type to "prospect" if not already "client"
    -   Set work type if converting existing lead
    -   Update lead source if contact is less than 14 days old
    -   Update campaign ID if provided and contact is recent
    -   Save contact updates within transaction

11. **Contact Counts Update** (`updateContactCounts`)

    -   Increment opportunity count by 1
    -   Increment referral count if referrer specified
    -   Increment lead count if new lead created
    -   Execute asynchronously (non-blocking)

12. **Transaction Commit**
    -   Commit all database changes
    -   Return success response with opportunity ID

### Phase 6: Error Handling & Cleanup

13. **Error Handling** (`handleCreateOpportunityError`)

    -   Abort transaction on any error
    -   Re-throw HTTP exceptions as-is
    -   Wrap other errors in `InternalServerErrorException`
    -   Log error details for debugging

14. **Session Cleanup**
    -   Always end database session in finally block
    -   Ensure proper resource cleanup

## Success Response

```typescript
{
    message: "Opportunity created successfully!",
    oppId: string // Generated opportunity ID
}
```

## Error Scenarios

-   Invalid input parameters → `BadRequestException`
-   Contact not found → `BadRequestException`
-   Default lead stage not found → `BadRequestException`
-   Database errors → `InternalServerErrorException`
-   Transaction failures → Automatic rollback

## Key Optimizations Applied

1. **Modular Design**: 19+ focused helper methods
2. **Type Safety**: Custom interfaces and proper typing
3. **Error Handling**: Comprehensive validation and error management
4. **Performance**: Optimized database queries and async operations
5. **Transaction Safety**: Proper session management and rollback
6. **Documentation**: Detailed JSDoc comments throughout

## Visual Flowchart

```mermaid
flowchart TD
    A[Start: createOpportunity] --> B[Input Validation]
    B --> C{Validation Passed?}
    C -->|No| D[Throw BadRequestException]
    C -->|Yes| E[Start DB Session & Transaction]

    E --> F[Clean Input Data]
    F --> G[Generate Opportunity ID]
    G --> H[Validate & Fetch Contact]

    H --> I{Contact Found?}
    I -->|No| J[Throw BadRequestException]
    I -->|Yes| K[Generate PO & Number]

    K --> L[Handle Contact Transfer]
    L --> M{Original Contact Different?}
    M -->|Yes| N[Transfer Active Lead]
    N --> O[Link Original to Target Contact]
    M -->|No| P[Skip Transfer]
    O --> P

    P --> Q[Handle Lead Management]
    Q --> R{Create New Lead?}

    R -->|Yes| S[Mark Existing Lead as Lost]
    S --> T[Create New Lead with Converted Status]
    T --> U[Add Contact Activity Log]

    R -->|No| V{Lead ID Provided?}
    V -->|Yes| W[Convert Specific Lead]
    V -->|No| X[Find Most Recent Active Lead]
    X --> Y{Active Lead Found?}
    Y -->|Yes| Z[Convert Found Lead]
    Y -->|No| AA[Create New Lead]

    W --> BB[Add Activity if Successful]
    Z --> BB
    AA --> BB
    U --> BB

    BB --> CC[Create Opportunity Record]
    CC --> DD[Build Comments Array]
    DD --> EE[Get Tax Jurisdiction]
    EE --> FF[Build Checkpoint Activity]
    FF --> GG[Clean DTO Data]
    GG --> HH{Warranty Type?}
    HH -->|Yes| II[Set Sale Date]
    HH -->|No| JJ[Skip Sale Date]
    II --> JJ

    JJ --> KK[Save Opportunity to DB]
    KK --> LL[Create Activity Record]
    LL --> MM[Update Contact Information]

    MM --> NN[Update Contact Type to Prospect]
    NN --> OO{Contact < 14 days old?}
    OO -->|Yes| PP[Update Lead Source & Campaign]
    OO -->|No| QQ[Skip Lead Source Update]
    PP --> QQ

    QQ --> RR[Update Contact Counts Async]
    RR --> SS[Commit Transaction]
    SS --> TT[Return Success Response]

    %% Error Handling
    D --> UU[End Session]
    J --> VV[Abort Transaction]
    VV --> UU

    %% Main Error Path
    CC --> WW{Error Occurred?}
    DD --> WW
    EE --> WW
    FF --> WW
    GG --> WW
    KK --> WW
    LL --> WW
    MM --> WW
    NN --> WW
    SS --> WW

    WW -->|Yes| XX[Abort Transaction]
    XX --> YY{HTTP Exception?}
    YY -->|Yes| ZZ[Re-throw Exception]
    YY -->|No| AAA[Wrap in InternalServerError]
    ZZ --> BBB[End Session]
    AAA --> BBB

    WW -->|No| TT
    TT --> CCC[End Session]
    BBB --> DDD[Throw Error]
    UU --> DDD
    CCC --> EEE[End: Success]

    %% Styling
    classDef startEnd fill:#e1f5fe
    classDef process fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef error fill:#ffebee
    classDef success fill:#e8f5e8

    class A,EEE startEnd
    class B,E,F,G,H,K,L,P,Q,S,T,U,W,Z,AA,BB,CC,DD,EE,FF,GG,JJ,KK,LL,MM,NN,QQ,RR,SS,TT,CCC process
    class C,I,M,R,V,Y,HH,OO,WW,YY decision
    class D,J,VV,XX,ZZ,AAA,BBB,DDD error
    class EEE success
```

## Detailed Method Breakdown

### Core Helper Methods

#### 1. `validateCreateOpportunityInput(memberId, companyId, opportunityDto)`

**Purpose**: Validates all input parameters before processing
**Validations**:

-   `memberId`: Must be non-empty string
-   `companyId`: Must be non-empty string
-   `opportunityDto.contactId`: Required field
-   `opportunityDto.oppType`: Required field
-   `opportunityDto.oppDate`: Required field
-   `opportunityDto.newLeadDate`: Required field

#### 2. `validateAndFetchContact(contactId, companyId)`

**Purpose**: Validates contact exists and fetches required data
**Database Query**:

```typescript
contactModel.findOne(
    { _id: contactId, companyId },
    { isBusiness: 1, lastName: 1, firstName: 1, businessName: 1, createdAt: 1, type: 1 },
);
```

**Returns**: `ContactForOpportunity` interface

#### 3. `generatePOAndNumber(contact, street, companyId, warrantyType)`

**Purpose**: Generates unique PO and opportunity numbers
**Logic**:

-   Extract display name from contact (business name > last name > first name > "UNKN")
-   Create PO using `createPO(name, street)` helper
-   Generate sequential number using `getNum()` method
    **Returns**: `{ PO: string, num: string }`

#### 4. `handleContactTransfer(createOpportunityDto, memberId, companyId, session)`

**Purpose**: Manages contact transfer when originalContact differs from contactId
**Process**:

1. Check if transfer is needed (`originalContact !== contactId`)
2. Find existing active leads for target contact
3. Transfer active lead from original to target contact
4. Mark existing lead as invalid if target already has active lead
5. Link original contact to target contact

#### 5. `handleLeadManagement(createOpportunityDto, opportunityId, memberId, companyId, session)`

**Purpose**: Orchestrates lead creation or conversion
**Branches**:

-   **New Lead Creation**: Creates new lead with "converted" status
-   **Existing Lead Conversion**: Converts active lead to opportunity
    **Sub-methods**:
-   `getDefaultLeadStage()`: Fetches company's default lead stage
-   `handleNewLeadCreation()`: Creates new lead
-   `handleExistingLeadConversion()`: Converts existing lead

#### 6. `createOpportunityRecord(opportunityId, PO, num, createOpportunityDto, companyId, warrantyType, session)`

**Purpose**: Creates the main opportunity database record
**Process**:

1. Build comments array from DTO and notes
2. Get tax jurisdiction for state (if only one exists)
3. Build checkpoint activity with timestamps
4. Clean DTO data (remove unwanted fields)
5. Set sale date for warranty opportunities
6. Create and save opportunity document

#### 7. `updateContactAfterOpportunityCreation(contact, createOpportunityDto, companyId, session)`

**Purpose**: Updates contact information after opportunity creation
**Updates**:

-   Contact type to "prospect" (if not already "client")
-   Work type (if converting existing lead)
-   Lead source and campaign (if contact is < 14 days old)

#### 8. `updateContactCounts(createOpportunityDto, companyId)`

**Purpose**: Updates contact count fields asynchronously
**Counts Updated**:

-   `opportunitiesCount`: +1
-   `referralsCount`: +1 (if referrer specified)
-   `leadsCount`: +1 (if new lead created)
    **Note**: Executes asynchronously to avoid blocking main flow

### Error Handling Strategy

#### Input Validation Errors

-   **Type**: `BadRequestException`
-   **Trigger**: Invalid or missing required parameters
-   **Action**: Immediate rejection before database operations

#### Business Logic Errors

-   **Type**: `BadRequestException`
-   **Examples**: Contact not found, default stage missing
-   **Action**: Transaction abort and session cleanup

#### Database Errors

-   **Type**: `InternalServerErrorException`
-   **Trigger**: Database connection issues, constraint violations
-   **Action**: Transaction rollback, error logging, session cleanup

#### Transaction Management

-   **Success Path**: Commit all changes atomically
-   **Error Path**: Abort transaction, rollback all changes
-   **Cleanup**: Always end session in finally block

### Performance Optimizations

1. **Database Queries**:

    - Selective field projection (only fetch needed fields)
    - Single queries where possible
    - Proper indexing on query fields

2. **Async Operations**:

    - Contact count updates are non-blocking
    - Error handling doesn't block success path

3. **Memory Management**:

    - Clean up DTO data before database operations
    - Proper session management and cleanup

4. **Transaction Efficiency**:
    - Minimize transaction scope
    - Batch related operations
    - Quick commit/rollback decisions

### Integration Points

#### External Dependencies

-   `ContactsService.increaseContactCounts()`: Async contact count updates
-   `cleanCreateData()`: Data sanitization helper
-   `createPO()`: PO number generation helper
-   `randomUUID()`: Unique ID generation

#### Database Models

-   `contactModel`: Contact data operations
-   `leadModel`: Lead management operations
-   `opportunityModel`: Opportunity creation
-   `activityModel`: Activity logging
-   `crmStageModel`: Stage data retrieval
-   `taxModel`: Tax jurisdiction lookup

#### Response Format

```typescript
CreatedResponse {
    message: "Opportunity created successfully!",
    oppId: string // Generated opportunity UUID
}
```

This comprehensive flow ensures data integrity, proper error handling, and optimal performance while maintaining clean, maintainable code structure.
