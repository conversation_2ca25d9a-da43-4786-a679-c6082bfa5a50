import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsEnum } from "class-validator";
import { PaginationDto } from "src/shared/dto/pagination.dto";
import { ContactTypeEnum } from "../enum/contact.enum";

export class GetSearchContactDto extends PaginationDto {
    @ApiPropertyOptional({ description: "type" })
    @IsOptional()
    @IsEnum(ContactTypeEnum)
    type?: ContactTypeEnum;

    @ApiPropertyOptional({ description: "search" })
    @IsOptional()
    search?: string;

    @ApiPropertyOptional({ description: "get limited data" })
    @IsOptional()
    fields?: any;

    @ApiPropertyOptional({ description: "id" })
    @IsOptional()
    id?: any;
}
