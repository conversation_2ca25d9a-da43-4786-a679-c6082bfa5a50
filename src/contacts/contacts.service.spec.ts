import { Test, TestingModule } from "@nestjs/testing";
import { ContactsService } from "./contacts.service";
import { getModelToken, getConnectionToken } from "@nestjs/mongoose";
import { buildMongoQuery } from "../shared/helpers/logics";

describe("ContactsService - Count Filters", () => {
    let service: ContactsService;
    let mockContactModel: any;
    let mockOpportunityModel: any;
    let mockConnection: any;

    beforeEach(async () => {
        // Mock models and connection
        mockContactModel = {
            aggregate: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
        };

        mockOpportunityModel = {
            aggregate: jest.fn(),
            find: jest.fn(),
        };

        mockConnection = {
            startSession: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                ContactsService,
                { provide: getModelToken("Contact"), useValue: mockContactModel },
                { provide: getModelToken("Opportunity"), useValue: mockOpportunityModel },
                { provide: getModelToken("Lead"), useValue: {} },
                { provide: getModelToken("Project"), useValue: {} },
                { provide: getModelToken("Price"), useValue: {} },
                { provide: getModelToken("Client"), useValue: {} },
                { provide: getModelToken("ActivityLog"), useValue: {} },
                { provide: getModelToken("OpportunityActivity"), useValue: {} },
                { provide: getModelToken("Referrers"), useValue: {} },
                { provide: getModelToken("CrmStage"), useValue: {} },
                { provide: getConnectionToken(), useValue: mockConnection },
            ],
        }).compile();

        service = module.get<ContactsService>(ContactsService);
    });

    describe("buildMongoQuery", () => {
        it('should handle "more" operator for opportunities count field', () => {
            const filterPayload = {
                filter: [{ field: "opportunities", operator: "more", value: 3 }],
                logic: "AND",
            };

            const result = buildMongoQuery(filterPayload);
            expect(result.opportunities).toEqual({ $gt: 3 });
        });

        it('should handle "more" operator for referrer count field', () => {
            const filterPayload = {
                filter: [{ field: "referrer", operator: "more", value: 4 }],
                logic: "AND",
            };

            const result = buildMongoQuery(filterPayload);
            expect(result.referrer).toEqual({ $gt: 4 });
        });

        it('should handle "more" operator for createdAt date field with days unit', () => {
            const filterPayload = {
                filter: [{ field: "createdAt", operator: "more", value: 7, unit: "days" }],
                logic: "AND",
            };

            const result = buildMongoQuery(filterPayload);
            expect(result.createdAt).toHaveProperty("$gt");
            expect(result.createdAt.$gt).toBeInstanceOf(Date);
        });

        it('should handle "less" operator for count fields', () => {
            const filterPayload = {
                filter: [{ field: "opportunities", operator: "less", value: 5 }],
                logic: "AND",
            };

            const result = buildMongoQuery(filterPayload);
            expect(result.opportunities).toEqual({ $lt: 5 });
        });

        it('should handle "range" operator for count fields', () => {
            const filterPayload = {
                filter: [{ field: "opportunities", operator: "range", min: 2, max: 10 }],
                logic: "AND",
            };

            const result = buildMongoQuery(filterPayload);
            expect(result.opportunities).toEqual({ $gte: 2, $lte: 10 });
        });

        it('should handle "range" operator for date fields', () => {
            const startDate = new Date("2023-01-01");
            const endDate = new Date("2023-12-31");
            const filterPayload = {
                filter: [{ field: "createdAt", operator: "range", startDate, endDate }],
                logic: "AND",
            };

            const result = buildMongoQuery(filterPayload);
            expect(result.createdAt).toEqual({ $gte: startDate, $lte: endDate });
        });
    });

    describe("isCountField", () => {
        it("should identify opportunities as a count field", () => {
            const result = service["isCountField"]("opportunities", { $gt: 3 });
            expect(result).toBe(true);
        });

        it("should identify referrer as a count field", () => {
            const result = service["isCountField"]("referrer", { $lt: 5 });
            expect(result).toBe(true);
        });

        it("should not identify regular fields as count fields", () => {
            const result = service["isCountField"]("fullName", { $regex: "test" });
            expect(result).toBe(false);
        });

        it("should not identify date fields as count fields", () => {
            const result = service["isCountField"]("createdAt", { $gt: new Date() });
            expect(result).toBe(false);
        });
    });

    describe("addCountFiltersToPipeline", () => {
        it("should add stored count field filter for opportunities", () => {
            const pipeline: any[] = [];
            const countFilters = { opportunities: { $gt: 3 } };
            const companyId = "test-company-id";

            service["addCountFiltersToPipeline"](pipeline, countFilters, companyId);

            expect(pipeline.length).toBe(1);
            expect(pipeline[0].$match).toEqual({
                opportunitiesCount: { $gt: 3 },
            });
        });

        it("should add stored count field filter for referrals", () => {
            const pipeline: any[] = [];
            const countFilters = { referrer: { $gt: 4 } };
            const companyId = "test-company-id";

            service["addCountFiltersToPipeline"](pipeline, countFilters, companyId);

            expect(pipeline.length).toBe(1);
            expect(pipeline[0].$match).toEqual({
                referralsCount: { $gt: 4 },
            });
        });

        it("should combine multiple count filters in single match stage", () => {
            const pipeline: any[] = [];
            const countFilters = {
                opportunities: { $gt: 3 },
                referrer: { $gt: 4 },
                leads: { $lt: 2 },
            };
            const companyId = "test-company-id";

            service["addCountFiltersToPipeline"](pipeline, countFilters, companyId);

            expect(pipeline.length).toBe(1);
            expect(pipeline[0].$match).toEqual({
                opportunitiesCount: { $gt: 3 },
                referralsCount: { $gt: 4 },
                leadsCount: { $lt: 2 },
            });
        });

        it("should not add any stages when no count filters provided", () => {
            const pipeline: any[] = [];
            const countFilters = {};
            const companyId = "test-company-id";

            service["addCountFiltersToPipeline"](pipeline, countFilters, companyId);

            expect(pipeline.length).toBe(0);
        });
    });

    describe("updateContactCounts", () => {
        it("should update all count fields for a contact", async () => {
            const contactId = "test-contact-id";
            const companyId = "test-company-id";

            // Mock the count queries
            const mockOpportunityModel = {
                countDocuments: jest.fn()
                    .mockResolvedValueOnce(5) // opportunities count
                    .mockResolvedValueOnce(3) // referrals count
            };
            const mockLeadModel = {
                countDocuments: jest.fn().mockResolvedValue(2) // leads count
            };
            const mockContactModel = {
                updateOne: jest.fn().mockResolvedValue({ modifiedCount: 1 })
            };

            // Replace the models with mocks
            (service as any).opportunityModel = mockOpportunityModel;
            (service as any).leadModel = mockLeadModel;
            (service as any).contactModel = mockContactModel;

            await service.updateContactCounts(contactId, companyId);

            expect(mockOpportunityModel.countDocuments).toHaveBeenCalledTimes(2);
            expect(mockLeadModel.countDocuments).toHaveBeenCalledTimes(1);
            expect(mockContactModel.updateOne).toHaveBeenCalledWith(
                { _id: contactId, companyId },
                {
                    $set: {
                        opportunitiesCount: 5,
                        referralsCount: 3,
                        leadsCount: 2,
                    },
                }
            );
        });
    });
});
});
