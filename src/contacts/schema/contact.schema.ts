import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";
import { ContactTypeEnum } from "../enum/contact.enum";

export type ContactDocument = Contact & Document;

class Attribution {
    @Prop({ required: true })
    sessionSource: string;

    @Prop({ required: true })
    utmSource: string;

    @Prop({ required: true })
    utmMedium: string;

    @Prop({ required: true })
    utmContent: string;

    @Prop({ required: true })
    utmCampaign: string;

    @Prop({ required: true })
    utmTerm: string;

    @Prop({ required: true })
    utmKeyword: string;

    @Prop({ required: true })
    utmMatchType: string;

    @Prop({ required: true })
    referringWebpage: string;

    @Prop({ required: true })
    adSetId: string;

    @Prop({ required: true })
    googleClickId: string;

    @Prop({ required: true })
    adName: string;

    @Prop({ required: true })
    gaClientId: string;

    @Prop({ required: true })
    userAgent: string;

    @Prop({ required: true })
    url: string;

    @Prop({ required: true })
    ip: string;

    @Prop({ required: true })
    adGroupId: string;

    @Prop({ required: true })
    gbraId: string;

    @Prop({ required: true })
    wbraId: string;

    @Prop({ required: true })
    fbr: string;

    @Prop({ required: true })
    fbp: string;

    @Prop({ required: true })
    form: string;

    @Prop({ required: true })
    formId: string;

    @Prop()
    createdAt: Date;
}

class Dnd {
    @Prop({ default: false })
    email: boolean;

    @Prop({ default: false })
    sms: boolean;

    @Prop({ default: false })
    voice: boolean;

    @Prop({ default: false })
    gbp: boolean;

    @Prop({ default: false })
    inbound: boolean;
}

@Schema({ timestamps: true, id: false, collection: "Contact", strict: false })
export class Contact {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @Prop({ required: true })
    fullName: string;

    @Prop({ type: Date, required: false })
    dateOfBirth?: Date;

    @Prop({ required: true })
    firstName: string;

    @Prop()
    lastName?: string;

    @Prop()
    street?: string;

    @Prop()
    city?: string;

    @Prop()
    state?: string;

    @Prop()
    zip?: string;

    @Prop()
    fullAddress?: string;

    @Prop()
    lat?: number; // latitude

    @Prop()
    long?: number; // longitude

    @Prop()
    phone?: string;

    @Prop()
    notes?: string;

    @Prop()
    email?: string;

    @Prop({ default: false })
    isBusiness?: boolean;

    @Prop()
    businessName?: string;

    @UUIDProp()
    companyId: string;

    @Prop({ required: true, enum: ContactTypeEnum, default: ContactTypeEnum.OTHER })
    type: ContactTypeEnum;

    @Prop({ type: Date, required: true })
    dateReceived: Date;

    // @UUIDProp({ required: false })
    // appointmentSetter?: string;

    @UUIDProp({ required: false })
    salesPersonId?: string;

    @UUIDProp({ required: false })
    projectManagerId?: string;

    @Prop({ required: false })
    workType?: string;

    @UUIDProp({ required: false })
    leadSourceId?: string;

    @UUIDProp({ required: false })
    campaignId?: string;

    @Prop({ required: false })
    referredBy?: string; // referrer id

    @Prop({ default: false })
    selfGen: boolean;

    @Prop({ type: [String], default: [] })
    tags: string[];

    @Prop({ type: [String], default: [] })
    automations: string[];

    @Prop({
        type: Dnd,
        required: false,
        default: { email: false, sms: false, voice: false, gbp: false, inbound: false },
    })
    dnd: Dnd;

    @Prop({ type: () => [Attribution], required: false, default: [] })
    tracking: Attribution[];

    @Prop({ type: [{ id: String, relationship: String }], default: [], _id: false })
    linkedContacts?: Array<{ id: string; relationship: string }>;

    // Lead fields
    @Prop({ default: "active" })
    status: string;

    @Prop()
    lostDate?: Date;

    @Prop({ required: false })
    invalidLeadReason?: string;

    @Prop({ required: false })
    lostReason?: string;

    @UUIDProp({ required: false })
    csrId?: string; // appointmentSetter

    @UUIDProp({ required: false })
    stageId?: string;

    @Prop()
    unLostReason?: string;

    @Prop()
    unLostDate?: Date;

    @Prop()
    unLostBy?: string;

    @Prop()
    lostBy: string;

    @Prop({ type: Object })
    checkpointActivity: any;

    @Prop()
    createdBy: string;

    @Prop({ default: false })
    zapierLead: boolean;

    @Prop()
    newLeadDate: Date;

    @Prop({ type: [Object] })
    actions: any[];

    @Prop({ type: Object })
    nextAction: Record<string, any>;

    // @Prop()
    // todoCheck: boolean;

    @Prop({ type: [Object] })
    comments: any[];

    @Prop({ required: false })
    oppId?: string;

    @Prop({ required: false })
    oppDate?: Date;

    @Prop({ default: false })
    deleted: boolean;

    @Prop({ type: [Object] })
    orders: any[];

    // Precomputed count fields for performance optimization
    @Prop({ type: Number, default: 0 })
    opportunitiesCount: number;

    @Prop({ type: Number, default: 0 })
    referralsCount: number;

    @Prop({ type: Number, default: 0 })
    leadsCount: number;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

export const ContactSchema = SchemaFactory.createForClass(Contact);

// Indexes for performance
// ContactSchema.index({ _id: 1, companyId: 1, deleted: 1 });
// ContactSchema.index({ companyId: 1, fullName: 1, deleted: 1 });
// ContactSchema.index({ companyId: 1, phone: 1, deleted: 1 });
// ContactSchema.index({ companyId: 1, email: 1, deleted: 1 });
// ContactSchema.index({ companyId: 1, linkedContacts: 1, deleted: 1 });
