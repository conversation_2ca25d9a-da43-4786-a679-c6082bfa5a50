import { <PERSON>hem<PERSON>, SchemaFactory, Prop } from "@nestjs/mongoose";
import { randomUUID } from "crypto";
import { Document } from "mongoose";
import { UUIDProp } from "src/shared/decorator/uuid.decorator";

export type LeadDocument = Lead & Document;

@Schema({ timestamps: true, id: false, collection: "Lead", strict: true })
export class Lead {
    @Prop({ type: String, default: () => randomUUID() })
    _id: string;

    @UUIDProp()
    companyId: string;

    @UUIDProp({ required: false })
    contactId: string;

    @UUIDProp({ required: false })
    oppId?: string;

    @UUIDProp({ required: false })
    campaignId?: string;

    @UUIDProp({ required: false })
    leadSourceId: string;

    @UUIDProp({ required: false })
    trackingRuleId?: string;

    @Prop({ required: false })
    workType: string;

    email: string;
    firstName: string;
    lastName: string;
    phone: string;

    @Prop()
    csrId: string;

    @Prop()
    stageId: string;

    @Prop({ default: "active" })
    status: string;

    @Prop()
    referredBy: string;

    @Prop()
    createdBy: string;

    @Prop({ default: false })
    deleted: boolean;

    @Prop()
    newLeadDate: Date;

    @Prop()
    lostDate: Date;

    @Prop({ type: Object, required: false, default: null })
    lostReason?: {
        reason: string;
        notes: string;
    };

    @UUIDProp({ required: false })
    lostBy?: string;

    @Prop()
    unLostReason: string;

    @Prop()
    unLostDate: Date;

    @Prop()
    unLostBy: string;

    @Prop({
        type: Object,
        required: false,
        default: null,
    })
    invalidLeadReason?: {
        reason: string;
        notes: string;
    };

    @Prop({ type: Object })
    checkpointActivity: any;

    @Prop({ type: [Object], default: [] })
    statusChanges: any[];

    @Prop({ type: Object, required: false })
    tracking: any;

    @Prop({ type: Object, required: false })
    rawTracking: any;

    @Prop({ default: false })
    zapierLead: boolean;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;
}

export const LeadSchema = SchemaFactory.createForClass(Lead);
