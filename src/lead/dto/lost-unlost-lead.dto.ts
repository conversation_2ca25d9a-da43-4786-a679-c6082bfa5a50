import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";

export class LostUnLostLeadDto {
    @ApiProperty({ description: "reason", required: true })
    @IsString()
    @IsNotEmpty()
    reason: string;

    @ApiProperty({ description: "notes", required: false })
    @IsString()
    @IsOptional()
    notes?: string;

    @ApiProperty({ description: "lost date", required: true })
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    date: Date;
}
