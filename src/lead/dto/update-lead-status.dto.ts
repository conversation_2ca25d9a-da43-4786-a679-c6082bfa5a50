import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsOptional, IsString } from "class-validator";
import { ContactTypeEnum } from "src/contacts/enum/contact.enum";

export class UpdateLeadStatusDto {
    @ApiPropertyOptional({ description: "Invalid reason" })
    @IsOptional()
    @IsString()
    reason?: string;

    @ApiPropertyOptional({ description: "Additional notes for the invalid lead reason" })
    @IsOptional()
    @IsString()
    notes?: string;

    @ApiPropertyOptional({ description: "contact type enum" })
    @IsOptional()
    @IsEnum(ContactTypeEnum)
    type?: ContactTypeEnum;
}
