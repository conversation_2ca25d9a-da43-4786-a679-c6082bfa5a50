import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { OpportunityController } from "./opportunity.controller";
import { OpportunityService } from "./opportunity.service";
import { OpportunitySchema } from "src/opportunity/schema/opportunity.schema";
import { ClientSchema } from "src/client/schema/client.schema";
import { CompanyPaySchema } from "src/company/schema/company-pay.schema";
import { CompanySettingSchema } from "src/company/schema/company-setting.schema";
import { CompensationSchema } from "src/compensation/schema/compensation.schema";
import { CrmCheckpointSchema } from "src/crm/schema/crm-checkpoint.schema";
import { LeadSchema } from "src/lead/schema/lead.schema";
import { OrderSchema } from "src/project/schema/order.schema";
import { ProjectTypeSchema } from "src/project/schema/project-type.schema";
import { ProjectSchema } from "src/project/schema/project.schema";
import { TaxJurisdictionSchema } from "src/project/schema/tax-jurisdiction.schema";
import { CommissionModificationSchema } from "./schema/opp-commission.schema";
import { CrmStepSchema } from "src/crm/schema/crm-step.schema";
import { S3Module } from "src/s3/s3.module";
import { FormSchema } from "./schema/form.schema";
import { PositionModule } from "src/position/position.module";
import { CrmModule } from "src/crm/crm.module";
import { FormBuilderModule } from "src/form-builder/form-builder.module";
import { MailModule } from "src/mail/mail.module";
import { MemberSchema } from "src/company/schema/member.schema";
import { RoleSchema } from "src/role/schema/role.schema";
import { ContactSchema } from "src/contacts/schema/contact.schema";
import { ActivityLogSchema } from "src/activity-log/schema/activity-log.schema";
import { CrmStageSchema } from "src/crm/schema/crm-stage.schema";
import { ContactsModule } from "src/contacts/contacts.module";

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: "Opportunity", schema: OpportunitySchema },
            { name: "Lead", schema: LeadSchema },
            { name: "CrmCheckpoint", schema: CrmCheckpointSchema },
            { name: "CrmStep", schema: CrmStepSchema },
            { name: "CompanySetting", schema: CompanySettingSchema },
            { name: "ActivityLog", schema: ActivityLogSchema },
            { name: "Contact", schema: ContactSchema },
            { name: "Project", schema: ProjectSchema },
            { name: "TaxJurisdiction", schema: TaxJurisdictionSchema },
            { name: "CommissionModification", schema: CommissionModificationSchema },
            { name: "Compensation", schema: CompensationSchema },
            { name: "CompanyPay", schema: CompanyPaySchema },
            { name: "ProjectType", schema: ProjectTypeSchema },
            { name: "Order", schema: OrderSchema },
            { name: "Forms", schema: FormSchema },
            { name: "Member", schema: MemberSchema },
            { name: "Role", schema: RoleSchema },
            { name: "CrmStage", schema: CrmStageSchema },
        ]),
        S3Module,
        PositionModule,
        CrmModule,
        MailModule,
        FormBuilderModule,
        ContactsModule,
    ],
    controllers: [OpportunityController],
    providers: [OpportunityService],
    exports: [OpportunityService],
})
export class OpportunityModule {}
