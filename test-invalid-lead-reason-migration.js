/**
 * Test script to verify the invalidLeadReason migration
 * This script demonstrates the migration from string to object structure
 */

const mongoose = require('mongoose');

// Mock data to test the migration logic
const testData = [
    { _id: '1', invalidLeadReason: 'Current Client' },
    { _id: '2', invalidLeadReason: 'Spam' },
    { _id: '3', invalidLeadReason: 'Looking For Work' },
    { _id: '4', invalidLeadReason: 'Outside Service Area' },
    { _id: '5', invalidLeadReason: 'Purchase Material Only' },
    { _id: '6', invalidLeadReason: 'Service Not Provided' },
    { _id: '7', invalidLeadReason: 'Unreachable' },
    { _id: '8', invalidLeadReason: 'Vendor' },
    { _id: '9', invalidLeadReason: 'Warranty Call' },
    { _id: '10', invalidLeadReason: 'Some Custom Reason' },
    { _id: '11', invalidLeadReason: 'Another Unknown Value' }
];

// Known reasons mapping
const knownReasons = [
    'current client',
    'looking for work', 
    'outside service area',
    'purchase material only',
    'service not provided',
    'spam',
    'unreachable',
    'vendor',
    'warranty call'
];

function migrateInvalidLeadReason(oldReason) {
    const normalizedReason = oldReason.trim().toLowerCase();
    
    if (knownReasons.includes(normalizedReason)) {
        return {
            reason: oldReason.trim(),
            notes: ""
        };
    } else {
        return {
            reason: "Other (Describe in notes)",
            notes: oldReason.trim()
        };
    }
}

// Test the migration logic
console.log('Testing invalidLeadReason migration logic:\n');

testData.forEach(item => {
    const oldValue = item.invalidLeadReason;
    const newValue = migrateInvalidLeadReason(oldValue);
    
    console.log(`ID: ${item._id}`);
    console.log(`  Old: "${oldValue}"`);
    console.log(`  New: ${JSON.stringify(newValue)}`);
    console.log('');
});

console.log('Migration test completed successfully!');
console.log('\nTo run the actual migration:');
console.log('1. Uncomment the migration call in contacts.service.ts constructor');
console.log('2. Start the application');
console.log('3. The migration will run automatically on startup');
console.log('4. Check the console logs for migration results');
console.log('5. Comment out the migration call after successful execution');
